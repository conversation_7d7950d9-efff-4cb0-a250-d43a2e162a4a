package co.brkz.payment.supplierpayments.domain

import co.brkz.payment.common.exceptions.RequestedAmountExceedsPayableException
import co.brkz.payment.core.models.MonetaryValue
import co.brkz.payment.supplierpayments.entities.Delivery
import co.brkz.payment.supplierpayments.entities.SupplierPayment
import co.brkz.payment.supplierpayments.entities.SupplierPaymentRequest
import co.brkz.payment.supplierpayments.entities.SupplierPaymentRequestStatus
import co.brkz.payment.supplierpayments.entities.SupplierPaymentStatus
import co.brkz.payment.supplierpayments.usecases.UpdateOrCreate
import co.brkz.payment.terms.models.PaymentTermType
import org.bson.types.ObjectId
import java.math.BigDecimal
import java.time.LocalDateTime

class SupplierPaymentRequestManager(paymentRequestValue: SupplierPaymentRequest) {

    private var paymentRequest = paymentRequestValue

    companion object {
        fun createCloneWithAmount(paymentRequest: SupplierPaymentRequest, updateOrCreate: UpdateOrCreate): SupplierPaymentRequest {
            val currency = paymentRequest.amount.currency
            val deliveryDate = updateOrCreate.delivery.deliveryDate
                ?.let { calculateDueDate(paymentRequest.supplierPaymentTerm.type, it) }
            val delivery = Delivery(
                ObjectId(updateOrCreate.delivery.id),
                updateOrCreate.delivery.referenceNumber,
                deliveryDate
            )
            return paymentRequest.copy(
                id = ObjectId(),
                amount = MonetaryValue(updateOrCreate.amount.amount, currency),
                purchaseOrder = updateOrCreate.purchaseOrder,
                delivery = delivery,
                balanceDueAmount = MonetaryValue(BigDecimal.ZERO, currency),
            )
        }

        private fun calculateDueDate(paymentTermType: PaymentTermType, deliveryDate: LocalDateTime): LocalDateTime? {
            val addedDaysCount = when (paymentTermType) {
                PaymentTermType.NET_7 -> 7
                PaymentTermType.NET_10 -> 10
                PaymentTermType.NET_15 -> 15
                PaymentTermType.NET_30 -> 30
                PaymentTermType.NET_45 -> 45
                PaymentTermType.NET_60 -> 60
                PaymentTermType.NET_90 -> 90
                PaymentTermType.DEFAULT -> 0
                PaymentTermType.END_OF_MONTH -> 0
                PaymentTermType.END_OF_NEXT_MONTH -> 0
                PaymentTermType.CASH_ON_PICK_UP -> 0
                PaymentTermType.PAY_IN_2 -> 0
                PaymentTermType.PAY_IN_3 -> 0
                PaymentTermType.PAY_IN_4 -> 0
                PaymentTermType.CUSTOM -> 0
            }

            return deliveryDate.plusDays(addedDaysCount.toLong())
        }
    }
    fun processPaymentCreation(newPayment: SupplierPayment, existingPayments: List<SupplierPayment>): SupplierPaymentRequest {
        val contributedAmount = getContributedAmount(newPayment) ?: return paymentRequest
        if (contributedAmount.amount > paymentRequest.balanceDueAmount.amount) {
            throw RequestedAmountExceedsPayableException(paymentRequest.id)
        }
        return processPaymentUpdate(newPayment, existingPayments)
    }

    fun processPaymentUpdate(payment: SupplierPayment, existingPayments: List<SupplierPayment>): SupplierPaymentRequest {
        val payments = existingPayments.filterNot { it.id == payment.id } + payment
        val madeAmount = getMadeAmount(payments)
        val balanceDueAmount = paymentRequest.amount.minus(madeAmount)
        val proofedAmount = getProofedAmount(payments)
        val newStatus = calculateStatus(proofedAmount, paymentRequest.amount.amount)
        paymentRequest = paymentRequest.copy(balanceDueAmount = balanceDueAmount, status = newStatus)
        return paymentRequest
    }

    private fun getMadeAmount(payments: List<SupplierPayment>): MonetaryValue {
        return payments.filterNot { it.status == SupplierPaymentStatus.DELETED }
            .mapNotNull { getContributedAmount(it) }
            .takeIf { it.isNotEmpty() }
            ?.reduce { acc, it -> acc.plus(it) }
            ?: MonetaryValue(BigDecimal.ZERO, paymentRequest.amount.currency)
    }

    private fun calculateStatus(proofedAmount: BigDecimal, fullAmount: BigDecimal): SupplierPaymentRequestStatus {
        return when {
            proofedAmount.compareTo(BigDecimal.ZERO) == 0 -> SupplierPaymentRequestStatus.UNPAID
            proofedAmount.compareTo(fullAmount) == 0 -> SupplierPaymentRequestStatus.PAID
            else -> SupplierPaymentRequestStatus.PARTIALLY_PAID
        }
    }

    private fun getProofedAmount(existingPayments: List<SupplierPayment>) = existingPayments
        .filter { it.status == SupplierPaymentStatus.PAID }
        .sumOf { getContributedAmount(it)?.amount ?: BigDecimal.ZERO }

    private fun getContributedAmount(payment: SupplierPayment): MonetaryValue? {
        return payment.paymentRequestsContributions.find { it.id == paymentRequest.id }?.amount
    }

    fun updateDeliveryDate(deliveryDate: LocalDateTime): SupplierPaymentRequest {
        val updatedDelivery = paymentRequest.delivery.copy(deliveryDate = deliveryDate)
        val dueDate = calculateDueDate(paymentRequest.supplierPaymentTerm.type, deliveryDate)
        paymentRequest = paymentRequest.copy(dueDate = dueDate, delivery = updatedDelivery)
        return paymentRequest
    }


    fun updateAmount(amount: MonetaryValue, payments: List<SupplierPayment>): SupplierPaymentRequest {
        val newAmount = amount
        val madeAmount = getMadeAmount(payments)
        val newBalanceDueAmount = newAmount.minus(madeAmount)
        val proofedAmount = getProofedAmount(payments)
        val newStatus = calculateStatus(proofedAmount, newAmount.amount)
        paymentRequest = paymentRequest.copy(
            amount = newAmount,
            balanceDueAmount = newBalanceDueAmount,
            status = newStatus,
        )
        return paymentRequest
    }
}
