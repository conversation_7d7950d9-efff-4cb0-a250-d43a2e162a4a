package co.brkz.payment.supplierpayments.usecases

import co.brkz.payment.core.models.MonetaryValue
import co.brkz.payment.supplierpayments.domain.SupplierPaymentManager
import co.brkz.payment.supplierpayments.domain.SupplierPaymentRequestManager
import co.brkz.payment.supplierpayments.dtos.DeliveryDTO
import co.brkz.payment.supplierpayments.entities.PurchaseOrder
import co.brkz.payment.supplierpayments.entities.SupplierPayment
import co.brkz.payment.supplierpayments.entities.SupplierPaymentRequest
import co.brkz.payment.supplierpayments.repositories.SupplierPaymentRequestsRepository
import co.brkz.payment.supplierpayments.repositories.SupplierPaymentsRepository
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.math.BigDecimal
import kotlin.jvm.optionals.getOrNull

@Service
class HandleDeliveryUpdated(
    private val paymentsRepository: SupplierPaymentsRepository,
    private val paymentRequestsRepository: SupplierPaymentRequestsRepository,
) {

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }

    fun execute(request: HandleDeliveryUpdatedRequest): List<SupplierPaymentRequest?> {
        val mainPaymentRequest = paymentRequestsRepository.findById(ObjectId(request.main.id))
            .orElseThrow { IllegalArgumentException("Payment request not found") }


        val secondaryPaymentRequestAmount = request.secondary.id?.let {
            paymentRequestsRepository.findById(ObjectId(it)).getOrNull()?.amount
        } ?: MonetaryValue.withSAR(BigDecimal.ZERO.toString())

        if (request.main.amount.plus(request.secondary.amount) != mainPaymentRequest.amount.plus(secondaryPaymentRequestAmount)) {
            logAmounts(request, mainPaymentRequest.amount, secondaryPaymentRequestAmount)
            throw IllegalArgumentException("Updated payment requests amounts do not match the existing payment requests amounts")
        }

        // TODO fix the clone to use purchaseOrder and delivery
        val secondaryPaymentRequest = SupplierPaymentRequestManager.createCloneWithAmount(mainPaymentRequest,request.secondary)

        val mainPayments = paymentsRepository.findByPaymentRequestId(mainPaymentRequest.id)
        // TODO if no payments, change directly
        val mainRatio = request.main.amount.amount / mainPaymentRequest.amount.amount
        val secondaryRatio = request.secondary.amount.amount / mainPaymentRequest.amount.amount
        val updatedPayments = mainPayments.map { payment ->
            val paymentManager = SupplierPaymentManager(payment)
            val updatedPayment = paymentManager.convertContributionToCollective(mainPaymentRequest.id, mainRatio, secondaryPaymentRequest.id, secondaryRatio)
            updatedPayment
        }

        val updatedPaymentRequests = listOf(mainPaymentRequest, secondaryPaymentRequest)
            .zip(listOf(request.main.amount, request.secondary.amount))
            .map { (paymentRequest, newAmount) ->
                updatePaymentRequest(paymentRequest, updatedPayments, newAmount)
            }

        paymentsRepository.saveAll(updatedPayments)
        val savedPaymentRequests = paymentRequestsRepository.saveAll(updatedPaymentRequests)

        return savedPaymentRequests
    }

    private fun updatePaymentRequest(paymentRequest: SupplierPaymentRequest, updatedPayments: List<SupplierPayment>, newAmount: MonetaryValue): SupplierPaymentRequest {
        val manager = SupplierPaymentRequestManager(paymentRequest)
        val updatedPaymentRequest = manager.updateAmount(newAmount, updatedPayments)
        return updatedPaymentRequest
    }

    private fun logAmounts(request: HandleDeliveryUpdatedRequest, mainPaymentRequestAmount: MonetaryValue, secondaryPaymentRequestAmount: MonetaryValue) {
        logger.warn(
            "Updated payment requests amounts do not match the existing payment requests amounts. main id: ${request.main.id}" +
                "main: ${request.main.amount}, " +
                "secondary: ${request.secondary.amount}, " +
                "existing main: $mainPaymentRequestAmount, " +
                "existing secondary: $secondaryPaymentRequestAmount",
        )
    }
}

data class HandleDeliveryUpdatedRequest(
    val main: Update,
    val secondary: UpdateOrCreate,
)

data class Update(
    val id: String,
    val amount: MonetaryValue,
)

data class UpdateOrCreate(
    val id: String?,
    val purchaseOrder: PurchaseOrder,
    val amount: MonetaryValue,
    val delivery: DeliveryDTO,
)

// TODO extract in a separate file and add tests
fun <T> Iterable<T>.replaceFirst(predicate: ((T) -> Boolean)? = null, index: Int? = null, newItem: T): List<T> {
    return this.mapIndexed { i, item ->
        when {
            index != null && i == index -> newItem
            predicate != null && predicate(item) -> newItem
            else -> item
        }
    }.let { updated ->
        // Only replace the first match of predicate
        if (predicate != null && index == null) {
            var replaced = false
            updated.mapIndexed { i, item ->
                if (!replaced && predicate(this.elementAt(i))) {
                    replaced = true
                    newItem
                } else {
                    item
                }
            }
        } else {
            updated
        }
    }
}
