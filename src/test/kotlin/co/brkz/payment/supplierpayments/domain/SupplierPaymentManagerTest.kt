package co.brkz.payment.supplierpayments.domain

import co.brkz.payment.common.exceptions.MissingPaymentRequestContributionException
import co.brkz.payment.core.models.MonetaryValue
import co.brkz.payment.supplierpayments.entities.PaymentRequestContribution
import co.brkz.payment.supplierpayments.entities.SupplierPaymentStatus
import co.brkz.payment.supplierpayments.fixtures.PAYMENT_REQUEST_ID1
import co.brkz.payment.supplierpayments.fixtures.PAYMENT_REQUEST_ID2
import co.brkz.payment.supplierpayments.fixtures.createSupplierPayment
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.Test
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.assertThrows
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

@RunWith(MockitoJUnitRunner::class)
class SupplierPaymentManagerTest {

    @Test
    fun `markPaymentAsPaid should update status and proof of payment`() {
        val supplierPaymentManager = createSupplierPaymentManager()

        val payment = supplierPaymentManager.markPaymentAsPaid("documentUrl")

        assertEquals(SupplierPaymentStatus.PAID, payment.status)
        assertEquals("documentUrl", payment.proof?.documentUrl)
        assertThat(payment.proof?.uploadedAt?.truncatedTo(ChronoUnit.SECONDS))
            .isEqualTo(LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS))
    }

    @Test
    fun `markPaymentAsPaid should throw IllegalStateException if payment request is already paid`() {
        val supplierPaymentManager = getPaidPayment()

        val exception = assertThrows<IllegalStateException>(fun() {
            supplierPaymentManager.markPaymentAsPaid("documentUrl")
        })

        assertEquals("Payment is already paid", exception.message)
    }

    @Test
    fun `getPaymentRequestContribution should throw IllegalStateException if no payment request contributions are existing`() {
        val payment = createSupplierPayment(SupplierPaymentStatus.AWAITING_APPROVAL).copy(paymentRequestsContributions = emptyList())
        val supplierPaymentManager = SupplierPaymentManager(payment)

        val exception = assertThrows<MissingPaymentRequestContributionException> {
            supplierPaymentManager.getPaymentRequestContributions()
        }

        assertEquals("Payment request contribution not found", exception.message)
    }

    @Test
    fun `convertContributionToCollective should convert contribution to collective by ratios - for single payment`() {
        val payment = createSupplierPayment(SupplierPaymentStatus.AWAITING_APPROVAL, "100")
        val manager = SupplierPaymentManager(payment)
        val supplierPaymentManager = SupplierPaymentManager(payment)

        val updatedPayment = supplierPaymentManager.convertContributionToCollective(
            PAYMENT_REQUEST_ID1, BigDecimal("0.7"),
            PAYMENT_REQUEST_ID2, BigDecimal("0.3")
        )

        assertEquals(2, updatedPayment.paymentRequestsContributions.size)
        assertEquals(BigDecimal("70.0"), updatedPayment.paymentRequestsContributions[0].amount.amount)
        assertEquals(BigDecimal("30.0"), updatedPayment.paymentRequestsContributions[1].amount.amount)
    }

    @Test
    fun `convertContributionToCollective should convert contribution to collective by ratios - for already collective payment`() {
        val payment = createSupplierPayment(SupplierPaymentStatus.AWAITING_APPROVAL, "250").copy(
            paymentRequestsContributions = listOf(
                PaymentRequestContribution(PAYMENT_REQUEST_ID1, MonetaryValue.withSAR("100")),
                PaymentRequestContribution(PAYMENT_REQUEST_ID2, MonetaryValue.withSAR("150")),
            ),
        )
        val manager = SupplierPaymentManager(payment)
        val supplierPaymentManager = SupplierPaymentManager(payment)

        val paymentRequest3Id = ObjectId("662e220f95451b4cc4aec862")
        val updatedPayment = supplierPaymentManager.convertContributionToCollective(
            PAYMENT_REQUEST_ID1, BigDecimal("0.7"),
            paymentRequest3Id, BigDecimal("0.3")
        )

        assertEquals(3, updatedPayment.paymentRequestsContributions.size)
        assertEquals(BigDecimal("70.0"), updatedPayment.paymentRequestsContributions[0].amount.amount)
        assertEquals(BigDecimal("150"), updatedPayment.paymentRequestsContributions[1].amount.amount)
        assertEquals(BigDecimal("30.0"), updatedPayment.paymentRequestsContributions[2].amount.amount)
    }

    private fun getPaidPayment(): SupplierPaymentManager {
        val supplierPaymentManager = createSupplierPaymentManager(SupplierPaymentStatus.AWAITING_APPROVAL)
        supplierPaymentManager.markPaymentAsPaid("documentUrl")
        return supplierPaymentManager
    }

    private fun createSupplierPaymentManager(status: SupplierPaymentStatus = SupplierPaymentStatus.AWAITING_APPROVAL) = SupplierPaymentManager(
        paymentValue = createSupplierPayment(status = status),
    )
}
