package co.brkz.payment.supplierpayments.usecases

import co.brkz.payment.core.models.MonetaryValue
import co.brkz.payment.supplierpayments.dtos.DeliveryDTO
import co.brkz.payment.supplierpayments.entities.PurchaseOrder
import co.brkz.payment.supplierpayments.entities.SupplierPayment
import co.brkz.payment.supplierpayments.entities.SupplierPaymentRequest
import co.brkz.payment.supplierpayments.entities.SupplierPaymentRequestStatus
import co.brkz.payment.supplierpayments.entities.SupplierPaymentStatus
import co.brkz.payment.supplierpayments.fixtures.PAYMENT_REQUEST_ID1
import co.brkz.payment.supplierpayments.fixtures.createSupplierPayment
import co.brkz.payment.supplierpayments.fixtures.createSupplierPaymentRequest
import co.brkz.payment.supplierpayments.repositories.SupplierPaymentRequestsRepository
import co.brkz.payment.supplierpayments.repositories.SupplierPaymentsRepository
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.argumentCaptor
import java.time.LocalDateTime
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

private const val PURCHASE_ORDER_2_ID = "662e220f95451b4cc4aec8af"
private const val DELIVERY_2_ID = "662e220f95451b4cc4aec8a3"
private const val DELIVERY_2_REF_NUMBER = "D03-021"
private const val PURCHASE_ORDER_2_PO_NUMBER = "PO-003"

@RunWith(MockitoJUnitRunner::class)
class HandleDeliveryUpdatedTest {

    @Mock
    private lateinit var paymentsRepository: SupplierPaymentsRepository

    @Mock
    private lateinit var paymentRequestsRepository: SupplierPaymentRequestsRepository

    private lateinit var useCase: HandleDeliveryUpdated

    @Before
    fun setup() {
        useCase = HandleDeliveryUpdated(
            paymentsRepository,
            paymentRequestsRepository,
        )
    }

    @Test
    fun `execute decrease the payment request amount and create new one - for unpaid payment request with no payments`() {
        // given
        val paymentRequestId = PAYMENT_REQUEST_ID1
        val request = createHandleDeliveryUpdatedRequest("7000", "3000")
        val paymentRequest = createSupplierPaymentRequest("10000", SupplierPaymentRequestStatus.UNPAID)

        `when`(paymentRequestsRepository.findById(paymentRequestId)).thenReturn(Optional.of(paymentRequest))
        `when`(paymentsRepository.findByPaymentRequestId(PAYMENT_REQUEST_ID1)).thenReturn(emptyList())
        `when`(paymentRequestsRepository.saveAll(Mockito.anyList()))
            .thenAnswer { it.arguments[0] as List<SupplierPaymentRequest> }

        // when
        val result = useCase.execute(request)

        // then
        assertEquals(2, result.size)
        val paymentRequestsCaptor = argumentCaptor<List<SupplierPaymentRequest>>()
        verify(paymentRequestsRepository).saveAll(paymentRequestsCaptor.capture())

        val capturedList = paymentRequestsCaptor.firstValue
        assertEquals(2, capturedList.size)
        assertEquals(PAYMENT_REQUEST_ID1, capturedList[0].id)
        assertEquals("7000", capturedList[0].amount.amount.toString())
        assertEquals("7000", capturedList[0].balanceDueAmount.amount.toString())
        assertEquals(SupplierPaymentRequestStatus.UNPAID, capturedList[0].status)
        assertNotNull(capturedList[1].id)
        assertEquals("3000", capturedList[1].amount.amount.toString())
        assertEquals("3000", capturedList[1].balanceDueAmount.amount.toString())
        assertEquals(SupplierPaymentRequestStatus.UNPAID, capturedList[1].status)
        assertEquals(PURCHASE_ORDER_2_ID, capturedList[1].purchaseOrder.id)
        assertEquals(PURCHASE_ORDER_2_PO_NUMBER, capturedList[1].purchaseOrder.poNumber)
        assertEquals(DELIVERY_2_ID, capturedList[1].delivery.id.toString())
        assertEquals(DELIVERY_2_REF_NUMBER, capturedList[1].delivery.referenceNumber)
    }


    @Test
    fun `execute decrease the payment request amount and create new one - for paid payment request with single payment`() {
        // given
        val paymentRequestId = PAYMENT_REQUEST_ID1
        val request = createHandleDeliveryUpdatedRequest("7000", "3000")
        val paymentRequest = createSupplierPaymentRequest("10000", SupplierPaymentRequestStatus.PAID)
            .copy(balanceDueAmount = MonetaryValue.withSAR("0"))
        val payment = createSupplierPayment(SupplierPaymentStatus.PAID, "10000")
        `when`(paymentRequestsRepository.findById(paymentRequestId)).thenReturn(Optional.of(paymentRequest))
        `when`(paymentsRepository.findByPaymentRequestId(PAYMENT_REQUEST_ID1)).thenReturn(listOf(payment))
        `when`(paymentRequestsRepository.saveAll(Mockito.anyList()))
            .thenAnswer { it.arguments[0] as List<SupplierPaymentRequest> }

        // when
        val result = useCase.execute(request)

        // then
        assertEquals(2, result.size)

        val paymentRequestsCaptor = argumentCaptor<List<SupplierPaymentRequest>>()
        verify(paymentRequestsRepository).saveAll(paymentRequestsCaptor.capture())

        val capturedList = paymentRequestsCaptor.firstValue
        assertEquals(2, capturedList.size)
        assertEquals(PAYMENT_REQUEST_ID1, capturedList[0].id)
        assertEquals("7000", capturedList[0].amount.amount.toString())
        assertEquals("0.0", capturedList[0].balanceDueAmount.amount.toString())
        assertEquals(SupplierPaymentRequestStatus.PAID, capturedList[0].status)
        assertNotNull(capturedList[1].id)
        assertEquals("3000", capturedList[1].amount.amount.toString())
        assertEquals("0.0", capturedList[1].balanceDueAmount.amount.toString())
        assertEquals(SupplierPaymentRequestStatus.PAID, capturedList[1].status)

        val paymentsCaptor = argumentCaptor<List<SupplierPayment>>()
        verify(paymentsRepository).saveAll(paymentsCaptor.capture())

        val paymentsCapturedList = paymentsCaptor.firstValue
        assertEquals(1, paymentsCapturedList.size)

        val capturedPayment = paymentsCapturedList[0]
        assertEquals(2, capturedPayment.paymentRequestsContributions.size)
        assertEquals(PAYMENT_REQUEST_ID1, capturedPayment.paymentRequestsContributions[0].id)
        assertEquals("7000.0", capturedPayment.paymentRequestsContributions[0].amount.amount.toString())
        assertEquals("3000.0", capturedPayment.paymentRequestsContributions[1].amount.amount.toString())
    }

    private fun createHandleDeliveryUpdatedRequest(amount: String = "7000", secondaryAmount: String = "3000"): HandleDeliveryUpdatedRequest {
        return HandleDeliveryUpdatedRequest(
            main = Update(
                id = PAYMENT_REQUEST_ID1.toHexString(),
                amount = MonetaryValue.withSAR(amount),
            ),
            secondary = UpdateOrCreate(
                id = null,
                purchaseOrder = PurchaseOrder(
                    id = PURCHASE_ORDER_2_ID,
                    poNumber = PURCHASE_ORDER_2_PO_NUMBER,
                ),
                amount = MonetaryValue.withSAR(secondaryAmount),
                delivery = DeliveryDTO(
                    id = DELIVERY_2_ID,
                    referenceNumber = DELIVERY_2_REF_NUMBER,
                    deliveryDate = LocalDateTime.now(),
                ),
            ),
        )
    }
}
