package co.brkz.payment.common.utilities

import org.junit.Test
import kotlin.test.assertEquals

class IterableExtTest {

    @Test
    fun `replaceFirst should replace element at specified index`() {
        val list = listOf("a", "b", "c", "d")
        val result = list.replaceFirst(index = 2, newItem = "X")
        
        assertEquals(listOf("a", "b", "X", "d"), result)
    }

    @Test
    fun `replaceFirst should replace element at index 0`() {
        val list = listOf("first", "second", "third")
        val result = list.replaceFirst(index = 0, newItem = "NEW")
        
        assertEquals(listOf("NEW", "second", "third"), result)
    }

    @Test
    fun `replaceFirst should replace element at last index`() {
        val list = listOf(1, 2, 3, 4, 5)
        val result = list.replaceFirst(index = 4, newItem = 99)
        
        assertEquals(listOf(1, 2, 3, 4, 99), result)
    }

    @Test
    fun `replaceFirst should not replace anything when index is out of bounds`() {
        val list = listOf("a", "b", "c")
        val result = list.replaceFirst(index = 5, newItem = "X")
        
        assertEquals(listOf("a", "b", "c"), result)
    }

    @Test
    fun `replaceFirst should not replace anything when index is negative`() {
        val list = listOf("a", "b", "c")
        val result = list.replaceFirst(index = -1, newItem = "X")
        
        assertEquals(listOf("a", "b", "c"), result)
    }

    @Test
    fun `replaceFirst should replace first element matching predicate`() {
        val list = listOf("apple", "banana", "cherry", "apple")
        val result = list.replaceFirst(predicate = { it == "apple" }, newItem = "orange")
        
        assertEquals(listOf("orange", "banana", "cherry", "apple"), result)
    }

    @Test
    fun `replaceFirst should replace only first match when multiple elements match predicate`() {
        val list = listOf(1, 2, 3, 2, 4, 2)
        val result = list.replaceFirst(predicate = { it == 2 }, newItem = 99)
        
        assertEquals(listOf(1, 99, 3, 2, 4, 2), result)
    }

    @Test
    fun `replaceFirst should not replace anything when predicate matches no elements`() {
        val list = listOf("cat", "dog", "bird")
        val result = list.replaceFirst(predicate = { it == "fish" }, newItem = "hamster")
        
        assertEquals(listOf("cat", "dog", "bird"), result)
    }

    @Test
    fun `replaceFirst should work with empty list`() {
        val list = emptyList<String>()
        val result = list.replaceFirst(predicate = { it == "anything" }, newItem = "something")
        
        assertEquals(emptyList<String>(), result)
    }

    @Test
    fun `replaceFirst should work with single element list using predicate`() {
        val list = listOf("only")
        val result = list.replaceFirst(predicate = { it == "only" }, newItem = "replaced")
        
        assertEquals(listOf("replaced"), result)
    }

    @Test
    fun `replaceFirst should work with single element list using index`() {
        val list = listOf(42)
        val result = list.replaceFirst(index = 0, newItem = 100)
        
        assertEquals(listOf(100), result)
    }

    @Test
    fun `replaceFirst should prioritize index over predicate when both are provided`() {
        val list = listOf("a", "b", "c", "d")
        val result = list.replaceFirst(
            predicate = { it == "a" }, 
            index = 2, 
            newItem = "X"
        )
        
        assertEquals(listOf("a", "b", "X", "d"), result)
    }

    @Test
    fun `replaceFirst should work with complex objects`() {
        data class Person(val name: String, val age: Int)
        
        val people = listOf(
            Person("Alice", 25),
            Person("Bob", 30),
            Person("Charlie", 25)
        )
        
        val result = people.replaceFirst(
            predicate = { it.age == 25 }, 
            newItem = Person("David", 35)
        )
        
        assertEquals(
            listOf(
                Person("David", 35),
                Person("Bob", 30),
                Person("Charlie", 25)
            ), 
            result
        )
    }

    @Test
    fun `replaceFirst should work with different data types`() {
        val mixedList = listOf<Any>("string", 42, true, 3.14)
        val result = mixedList.replaceFirst(predicate = { it is Int }, newItem = "replaced_int")
        
        assertEquals(listOf<Any>("string", "replaced_int", true, 3.14), result)
    }

    @Test
    fun `replaceFirst should handle null values in list`() {
        val listWithNulls = listOf("a", null, "c", null)
        val result = listWithNulls.replaceFirst(predicate = { it == null }, newItem = "not_null")
        
        assertEquals(listOf("a", "not_null", "c", null), result)
    }

    @Test
    fun `replaceFirst should replace with null value`() {
        val list = listOf("a", "b", "c")
        val result = list.replaceFirst(index = 1, newItem = null)
        
        assertEquals(listOf("a", null, "c"), result)
    }

    @Test
    fun `replaceFirst should work with large lists`() {
        val largeList = (1..1000).toList()
        val result = largeList.replaceFirst(predicate = { it == 500 }, newItem = 9999)
        
        val expected = largeList.toMutableList().apply { set(499, 9999) }
        assertEquals(expected, result)
    }

    @Test
    fun `replaceFirst should preserve original list immutability`() {
        val originalList = listOf("a", "b", "c")
        val result = originalList.replaceFirst(index = 1, newItem = "X")
        
        // Original list should remain unchanged
        assertEquals(listOf("a", "b", "c"), originalList)
        assertEquals(listOf("a", "X", "c"), result)
    }
}
